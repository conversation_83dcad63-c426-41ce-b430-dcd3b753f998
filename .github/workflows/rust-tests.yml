name: Rust CI

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

jobs:
    build:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Set up Rust toolchain
              uses: actions-rust-lang/setup-rust-toolchain@v1

            - name: Install formatting tools
              run: rustup component add clippy rustfmt

            - name: Run cargo fmt --check
              run: cargo fmt --all -- --check

            - name: Run Clippy (ignoring warnings)
              run: cargo clippy --all-targets --all-features -- -D warnings || true

            - name: Run Tests
              run: |
                  if cargo test --test unit_tests; then
                      echo "✅ Success: All tests passed!"
                  else
                      echo "❌ Error: Tests failed!"
                      exit 1
                  fi
