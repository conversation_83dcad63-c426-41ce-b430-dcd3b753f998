# Btrust Builders: Rust for Bitcoiners Week Two Exercises

## Instructions

Welcome! This exercise is designed to help you practice key Rust programming skills specifically for Bitcoin development. 
Your task is to complete the `TODO` items found in the source files located in the `src/` directory.

1. Fork this repository.
2. Go to the `Actions` tab and enable github workflow for your repository by clicking `I understand my ...`

<img src="https://github.com/btrust-builders/rust-week-2-exercises/blob/main/enable-github-actions.png" width="700" />

3. Clone your fork to your local computer.
4. **Explore the Code**
   - Open the `src/` directory and examine the Rust source files.
   - Look for code marked with `TODO`.
5. **Complete the TODOs**
   - Implement the missing logic where indicated.
   - Ensure your code is readable, idiomatic, and compiles without warnings.
6. **Test Your Code**
   - Run the tests using:
     ```bash
     cargo test --test unit_tests
     ```
7. **Format and Lint (Optional but Recommended)**
   - Format your code:
     ```bash
     cargo fmt
     ```
   - Run Clippy for linting:
     ```bash
     cargo clippy
     ```
8. Commit and push your changes to the `main` branch of your remote fork.

9. Confirm your forked repository has a green check mark.

<img src="https://github.com/btrust-builders/rust-week-2-exercises/blob/main/success.png" width="300" />

10. Submit your solution to this form: [Google form](https://forms.gle/a3ibaSHcqpaZWsnPA).

PS: You can commit and push as often as you like and GitHub Actions will re-evaluate your code every time.
You will need to look through the auto-grader logs (in the "Actions" tab) to see what exactly you got right or wrong.
